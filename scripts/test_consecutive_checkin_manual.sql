-- <PERSON><PERSON><PERSON> để test consecutive checkin tasks bằng cách thao tác database trực tiếp
-- S<PERSON> dụng script này trong development environment

-- 1. Tìm user và task IDs
SELECT
    u.id as user_id,
    u.email,
    at.id as task_id,
    at.task_identifier,
    at.name,
    at.points
FROM users u
CROSS JOIN activity_tasks at
WHERE u.email = '<EMAIL>' -- Thay bằng email test của bạn
AND at.task_identifier IN ('CONSECUTIVE_CHECKIN_3', 'CONSECUTIVE_CHECKIN_7', 'CONSECUTIVE_CHECKIN_30')
ORDER BY at.task_identifier;

-- 2. Tạo hoặc cập nhật progress để simulate streak
-- Thay thế user_id và task_id với values thật từ query trên

-- Test case 1: Simulate 2 ngày streak (chưa đạt milestone 3 ngày)
INSERT INTO user_task_progress (
    id, user_id, task_id,
    progress_value, streak_count,
    status, last_completed_at,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'USER_ID_HERE',  -- Thay bằng user ID thật
    'TASK_ID_HERE',  -- Thay bằng task ID của CONSECUTIVE_CHECKIN_3
    2, 2,
    'COMPLETED',
    NOW() - INTERVAL '1 day',  -- Last checkin = yesterday
    NOW(), NOW()
) ON CONFLICT (user_id, task_id)
DO UPDATE SET
    progress_value = 2,
    streak_count = 2,
    status = 'COMPLETED',
    last_completed_at = NOW() - INTERVAL '1 day',
    updated_at = NOW();

-- Test case 2: Simulate 6 ngày streak (đã hoàn thành 3 ngày, đang hướng tới 7 ngày)
INSERT INTO user_task_progress (
    id, user_id, task_id,
    progress_value, streak_count,
    status, last_completed_at,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'USER_ID_HERE',  -- Thay bằng user ID thật
    'TASK_ID_HERE',  -- Thay bằng task ID của CONSECUTIVE_CHECKIN_7
    6, 6,
    'COMPLETED',
    NOW() - INTERVAL '1 day',
    NOW(), NOW()
) ON CONFLICT (user_id, task_id)
DO UPDATE SET
    progress_value = 6,
    streak_count = 6,
    status = 'COMPLETED',
    last_completed_at = NOW() - INTERVAL '1 day',
    updated_at = NOW();

-- Test case 3: Simulate 29 ngày streak (sắp đạt milestone 30 ngày)
INSERT INTO user_task_progress (
    id, user_id, task_id,
    progress_value, streak_count,
    status, last_completed_at,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'USER_ID_HERE',  -- Thay bằng user ID thật
    'TASK_ID_HERE',  -- Thay bằng task ID của CONSECUTIVE_CHECKIN_30
    29, 29,
    'COMPLETED',
    NOW() - INTERVAL '1 day',
    NOW(), NOW()
) ON CONFLICT (user_id, task_id)
DO UPDATE SET
    progress_value = 29,
    streak_count = 29,
    status = 'COMPLETED',
    last_completed_at = NOW() - INTERVAL '1 day',
    updated_at = NOW();

-- Kiểm tra kết quả
SELECT
    u.email,
    at.task_identifier,
    at.name,
    utp.streak_count,
    utp.progress_value,
    utp.status,
    utp.last_completed_at,
    utp.points_earned
FROM user_task_progress utp
JOIN users u ON utp.user_id = u.id
JOIN activity_tasks at ON utp.task_id = at.id
WHERE u.email = '<EMAIL>'
AND at.task_identifier LIKE 'CONSECUTIVE_CHECKIN_%'
ORDER BY at.task_identifier;
