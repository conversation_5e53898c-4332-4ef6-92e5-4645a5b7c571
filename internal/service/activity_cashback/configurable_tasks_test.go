package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestConfigurableTaskIdentifiers tests the new configurable task identifiers
func TestConfigurableTaskIdentifiers(t *testing.T) {
	t.Run("Configurable_Task_Identifiers_Defined", func(t *testing.T) {
		// Test that new configurable task identifiers are properly defined
		assert.Equal(t, "CONSECUTIVE_CHECKIN", string(model.TaskIDConsecutiveCheckin))
		assert.Equal(t, "ACCUMULATED_TRADING", string(model.TaskIDAccumulatedTrading))
	})

	t.Run("Configurable_Task_Definitions_Exist", func(t *testing.T) {
		// Test that configurable task definitions exist in registry
		defConsecutive, exists := model.TaskDefinitionRegistry[model.TaskIDConsecutiveCheckin]
		assert.True(t, exists, "Configurable consecutive checkin task definition should exist")
		assert.Equal(t, "Consecutive Check-in", defConsecutive.DisplayName)
		assert.Equal(t, 0, defConsecutive.Points, "Configurable task should have 0 points (variable)")

		defAccumulated, exists := model.TaskDefinitionRegistry[model.TaskIDAccumulatedTrading]
		assert.True(t, exists, "Configurable accumulated trading task definition should exist")
		assert.Equal(t, "Accumulated Trading Volume", defAccumulated.DisplayName)
		assert.Equal(t, 0, defAccumulated.Points, "Configurable task should have 0 points (variable)")
	})

	t.Run("Legacy_Task_Identifiers_Still_Exist", func(t *testing.T) {
		// Test that legacy task identifiers still exist for backward compatibility
		assert.Equal(t, "CONSECUTIVE_CHECKIN_3", string(model.TaskIDConsecutiveCheckin3))
		assert.Equal(t, "CONSECUTIVE_CHECKIN_7", string(model.TaskIDConsecutiveCheckin7))
		assert.Equal(t, "CONSECUTIVE_CHECKIN_30", string(model.TaskIDConsecutiveCheckin30))

		assert.Equal(t, "ACCUMULATED_TRADING_10K", string(model.TaskIDAccumulatedTrading10K))
		assert.Equal(t, "ACCUMULATED_TRADING_50K", string(model.TaskIDAccumulatedTrading50K))
		assert.Equal(t, "ACCUMULATED_TRADING_100K", string(model.TaskIDAccumulatedTrading100K))
		assert.Equal(t, "ACCUMULATED_TRADING_500K", string(model.TaskIDAccumulatedTrading500K))
	})
}

// TestConsecutiveCheckinHandler tests the new configurable consecutive checkin handler
func TestConsecutiveCheckinHandler(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	handler := NewConsecutiveCheckinHandler(mockService)

	t.Run("Handler_Creation", func(t *testing.T) {
		assert.NotNil(t, handler)
		assert.Equal(t, model.TaskIDConsecutiveCheckin, handler.GetIdentifier())
		assert.Equal(t, "daily", handler.GetCategory())
	})

	t.Run("Handle_With_Default_Days", func(t *testing.T) {
		ctx := context.Background()
		userID := uuid.New()
		taskID := uuid.New()

		// Reset mock
		mockService.ExpectedCalls = nil
		mockService.Calls = nil

		// Setup mock expectations for default 3 days
		mockService.On("UpdateActivity", ctx, userID).Return(nil)
		mockService.On("GetTaskProgress", ctx, userID, taskID).Return(&model.UserTaskProgress{
			UserID: userID,
			TaskID: taskID,
		}, nil)
		mockService.On("UpdateStreak", ctx, userID, taskID, true).Return(nil)
		mockService.On("IncrementProgress", ctx, userID, taskID, 1).Return(nil)
		mockService.On("CompleteProgress", ctx, userID, taskID).Return(nil)

		// Create task without conditions (should use default 3 days)
		task := &model.ActivityTask{
			ID:        taskID,
			Frequency: model.FrequencyDaily,
			Points:    50,
		}

		data := map[string]interface{}{}

		err := handler.Handle(ctx, userID, task, data)
		assert.NoError(t, err)
		mockService.AssertExpectations(t)
	})

	t.Run("Handle_With_Custom_Days", func(t *testing.T) {
		ctx := context.Background()
		userID := uuid.New()
		taskID := uuid.New()

		// Reset mock
		mockService.ExpectedCalls = nil
		mockService.Calls = nil

		// Setup mock expectations for custom 7 days
		mockService.On("UpdateActivity", ctx, userID).Return(nil)
		mockService.On("GetTaskProgress", ctx, userID, taskID).Return(&model.UserTaskProgress{
			UserID: userID,
			TaskID: taskID,
		}, nil)
		mockService.On("UpdateStreak", ctx, userID, taskID, true).Return(nil)
		mockService.On("IncrementProgress", ctx, userID, taskID, 1).Return(nil)
		mockService.On("CompleteProgress", ctx, userID, taskID).Return(nil)

		// Create task with custom consecutive days
		consecutiveDays := 7
		task := &model.ActivityTask{
			ID:        taskID,
			Frequency: model.FrequencyDaily,
			Points:    200,
			Conditions: &model.TaskConditions{
				ConsecutiveDays: &consecutiveDays,
			},
		}

		data := map[string]interface{}{}

		err := handler.Handle(ctx, userID, task, data)
		assert.NoError(t, err)
		mockService.AssertExpectations(t)
	})
}

// TestConfigurableAccumulatedTradingHandler tests the new configurable accumulated trading handler
func TestConfigurableAccumulatedTradingHandler(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	handler := NewConfigurableAccumulatedTradingHandler(mockService)

	t.Run("Handler_Creation", func(t *testing.T) {
		assert.NotNil(t, handler)
		assert.Equal(t, model.TaskIDAccumulatedTrading, handler.GetIdentifier())
		assert.Equal(t, "trading", handler.GetCategory())
	})

	t.Run("Handle_With_Default_Volume", func(t *testing.T) {
		ctx := context.Background()
		userID := uuid.New()
		taskID := uuid.New()

		// Setup mock expectations for default 10K volume
		mockService.On("UpdateActivity", ctx, userID).Return(nil)
		mockService.On("GetTaskProgress", ctx, userID, taskID).Return(&model.UserTaskProgress{
			UserID: userID,
			TaskID: taskID,
		}, nil)
		mockService.On("GetUserTierInfo", ctx, userID).Return(&model.UserTierInfo{
			UserID:           userID,
			TradingVolumeUSD: decimal.NewFromFloat(15000), // 15K > 10K default
		}, nil)
		mockService.On("CompleteTaskWithPoints", ctx, userID, taskID, mock.Anything).Return(nil)

		// Create task without conditions (should use default 10K)
		task := &model.ActivityTask{
			ID:        taskID,
			Frequency: model.FrequencyOneTime,
			Points:    300,
		}

		data := map[string]interface{}{}

		err := handler.Handle(ctx, userID, task, data)
		assert.NoError(t, err)
		mockService.AssertExpectations(t)
	})

	t.Run("Handle_With_Custom_Volume", func(t *testing.T) {
		ctx := context.Background()
		userID := uuid.New()
		taskID := uuid.New()

		// Reset mock
		mockService.ExpectedCalls = nil
		mockService.Calls = nil

		// Setup mock expectations for custom 50K volume
		mockService.On("UpdateActivity", ctx, userID).Return(nil)
		mockService.On("GetTaskProgress", ctx, userID, taskID).Return(&model.UserTaskProgress{
			UserID: userID,
			TaskID: taskID,
		}, nil)
		mockService.On("GetUserTierInfo", ctx, userID).Return(&model.UserTierInfo{
			UserID:           userID,
			TradingVolumeUSD: decimal.NewFromFloat(75000), // 75K > 50K target
		}, nil)
		mockService.On("CompleteTaskWithPoints", ctx, userID, taskID, mock.Anything).Return(nil)

		// Create task with custom target volume
		targetVolume := 50000.0 // 50K USD (actual value, no conversion)
		task := &model.ActivityTask{
			ID:        taskID,
			Frequency: model.FrequencyOneTime,
			Points:    1000,
			Conditions: &model.TaskConditions{
				TargetVolume: &targetVolume,
			},
		}

		data := map[string]interface{}{}

		err := handler.Handle(ctx, userID, task, data)
		assert.NoError(t, err)
		mockService.AssertExpectations(t)
	})

	t.Run("Handle_Volume_Not_Reached", func(t *testing.T) {
		ctx := context.Background()
		userID := uuid.New()
		taskID := uuid.New()

		// Reset mock
		mockService.ExpectedCalls = nil
		mockService.Calls = nil

		// Setup mock expectations for volume not reached
		mockService.On("UpdateActivity", ctx, userID).Return(nil)
		mockService.On("GetTaskProgress", ctx, userID, taskID).Return(&model.UserTaskProgress{
			UserID: userID,
			TaskID: taskID,
		}, nil)
		mockService.On("GetUserTierInfo", ctx, userID).Return(&model.UserTierInfo{
			UserID:           userID,
			TradingVolumeUSD: decimal.NewFromFloat(5000), // 5K < 10K default
		}, nil)
		mockService.On("UpdateTaskProgress", ctx, userID, taskID, 5000).Return(nil)

		// Create task without conditions (should use default 10K)
		task := &model.ActivityTask{
			ID:        taskID,
			Frequency: model.FrequencyOneTime,
			Points:    300,
		}

		data := map[string]interface{}{}

		err := handler.Handle(ctx, userID, task, data)
		assert.NoError(t, err)
		mockService.AssertExpectations(t)
	})
}

// TestTaskConditionsValidation tests validation of task conditions
func TestTaskConditionsValidation(t *testing.T) {
	t.Run("Valid_ConsecutiveDays", func(t *testing.T) {
		days := 7
		conditions := &model.TaskConditions{
			ConsecutiveDays: &days,
		}
		assert.NotNil(t, conditions.ConsecutiveDays)
		assert.Equal(t, 7, *conditions.ConsecutiveDays)
	})

	t.Run("Valid_TargetVolume", func(t *testing.T) {
		volume := 50.0
		conditions := &model.TaskConditions{
			TargetVolume: &volume,
		}
		assert.NotNil(t, conditions.TargetVolume)
		assert.Equal(t, 50.0, *conditions.TargetVolume)
	})

	t.Run("Combined_Conditions", func(t *testing.T) {
		days := 7
		volume := 50.0
		conditions := &model.TaskConditions{
			ConsecutiveDays: &days,
			TargetVolume:    &volume,
		}
		assert.NotNil(t, conditions.ConsecutiveDays)
		assert.NotNil(t, conditions.TargetVolume)
		assert.Equal(t, 7, *conditions.ConsecutiveDays)
		assert.Equal(t, 50.0, *conditions.TargetVolume)
	})
}
