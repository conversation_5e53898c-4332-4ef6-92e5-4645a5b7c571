-- Migration to consolidate consecutive checkin and accumulated trading tasks
-- This migration creates new configurable tasks and updates existing ones

-- Add new configurable consecutive checkin task
INSERT INTO activity_tasks (
    category_id,
    name,
    description,
    frequency,
    task_identifier,
    points,
    conditions,
    verification_method,
    is_active,
    sort_order
) VALUES (
    1, -- Daily category
    'Consecutive Check-in',
    'Check in for consecutive days to earn points (configurable target)',
    'DAILY',
    'CONSECUTIVE_CHECKIN',
    50, -- Default points, can be overridden per task instance
    '{"consecutive_days": 3}', -- Default 3 days
    'AUTO',
    true,
    15
) ON CONFLICT (task_identifier) DO NOTHING;

-- Add new configurable accumulated trading task
INSERT INTO activity_tasks (
    category_id,
    name,
    description,
    frequency,
    task_identifier,
    points,
    conditions,
    verification_method,
    is_active,
    sort_order
) VALUES (
    3, -- Trading category
    'Accumulated Trading Volume',
    'Reach cumulative trading volume target to earn points (configurable target)',
    'ONE_TIME',
    'ACCUMULATED_TRADING',
    300, -- Default points, can be overridden per task instance
    '{"target_volume": 10.0}', -- Default 10K USD
    'AUTO',
    true,
    50
) ON CONFLICT (task_identifier) DO NOTHING;

-- Update existing consecutive checkin tasks to add conditions if they don't have them
UPDATE activity_tasks 
SET conditions = '{"consecutive_days": 3}'
WHERE task_identifier = 'CONSECUTIVE_CHECKIN_3' 
  AND (conditions IS NULL OR conditions::text = '{}' OR conditions::text = 'null');

UPDATE activity_tasks 
SET conditions = '{"consecutive_days": 7}'
WHERE task_identifier = 'CONSECUTIVE_CHECKIN_7' 
  AND (conditions IS NULL OR conditions::text = '{}' OR conditions::text = 'null');

UPDATE activity_tasks 
SET conditions = '{"consecutive_days": 30}'
WHERE task_identifier = 'CONSECUTIVE_CHECKIN_30' 
  AND (conditions IS NULL OR conditions::text = '{}' OR conditions::text = 'null');

-- Update existing accumulated trading tasks to add conditions if they don't have them
UPDATE activity_tasks 
SET conditions = '{"target_volume": 10.0}'
WHERE task_identifier = 'ACCUMULATED_TRADING_10K' 
  AND (conditions IS NULL OR conditions::text = '{}' OR conditions::text = 'null');

UPDATE activity_tasks 
SET conditions = '{"target_volume": 50.0}'
WHERE task_identifier = 'ACCUMULATED_TRADING_50K' 
  AND (conditions IS NULL OR conditions::text = '{}' OR conditions::text = 'null');

UPDATE activity_tasks 
SET conditions = '{"target_volume": 100.0}'
WHERE task_identifier = 'ACCUMULATED_TRADING_100K' 
  AND (conditions IS NULL OR conditions::text = '{}' OR conditions::text = 'null');

UPDATE activity_tasks 
SET conditions = '{"target_volume": 500.0}'
WHERE task_identifier = 'ACCUMULATED_TRADING_500K' 
  AND (conditions IS NULL OR conditions::text = '{}' OR conditions::text = 'null');

-- Add comments to legacy tasks to indicate they are for backward compatibility
UPDATE activity_tasks 
SET description = CONCAT(description, ' (Legacy - use CONSECUTIVE_CHECKIN with configurable days instead)')
WHERE task_identifier IN ('CONSECUTIVE_CHECKIN_3', 'CONSECUTIVE_CHECKIN_7', 'CONSECUTIVE_CHECKIN_30')
  AND description NOT LIKE '%Legacy%';

UPDATE activity_tasks 
SET description = CONCAT(description, ' (Legacy - use ACCUMULATED_TRADING with configurable volume instead)')
WHERE task_identifier IN ('ACCUMULATED_TRADING_10K', 'ACCUMULATED_TRADING_50K', 'ACCUMULATED_TRADING_100K', 'ACCUMULATED_TRADING_500K')
  AND description NOT LIKE '%Legacy%';

-- Create indexes for better performance on conditions queries
CREATE INDEX IF NOT EXISTS idx_activity_tasks_conditions_consecutive_days 
ON activity_tasks USING GIN ((conditions->'consecutive_days'));

CREATE INDEX IF NOT EXISTS idx_activity_tasks_conditions_target_volume 
ON activity_tasks USING GIN ((conditions->'target_volume'));

-- Add validation constraints to ensure conditions are properly formatted
-- This is a check constraint to ensure consecutive_days is a positive integer
ALTER TABLE activity_tasks 
ADD CONSTRAINT chk_consecutive_days_positive 
CHECK (
    conditions->'consecutive_days' IS NULL 
    OR (
        jsonb_typeof(conditions->'consecutive_days') = 'number' 
        AND (conditions->'consecutive_days')::int > 0
    )
);

-- This is a check constraint to ensure target_volume is a positive number
ALTER TABLE activity_tasks 
ADD CONSTRAINT chk_target_volume_positive 
CHECK (
    conditions->'target_volume' IS NULL 
    OR (
        jsonb_typeof(conditions->'target_volume') = 'number' 
        AND (conditions->'target_volume')::numeric > 0
    )
);
